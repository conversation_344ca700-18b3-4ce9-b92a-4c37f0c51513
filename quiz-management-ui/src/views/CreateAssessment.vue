<template>
  <HerbitProfessionalLayout
    title="Create Assessment"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Form Card -->
    <FormCard color="cyan">
          <form @submit.prevent="createAssessment" class="space-y-6">
              <!-- Assessment Name -->
              <div>
                <Label for="assessmentName" class="text-gray-300">Assessment Name</Label>
                <Input
                  id="assessmentName"
                  v-model="assessmentName"
                  placeholder="e.g. DevOps Basics"
                  required
                />
              </div>

              <!-- Assessment Description -->
              <div>
                <Label for="assessmentDescription" class="text-gray-300">Assessment Description</Label>
                <Input
                  id="assessmentDescription"
                  v-model="assessmentDescription"
                  placeholder="e.g. A comprehensive assessment of DevOps fundamentals"
                  required
                />
                <div class="mt-1 text-xs text-gray-400">
                  Provide a brief description of what this assessment covers
                </div>
              </div>

              <!-- Skill Selection (Multiple) -->
              <div>
                <Label class="text-gray-300">Select Skills</Label>
                <div class="mb-2 text-xs text-gray-400">You can select multiple skills for this assessment</div>
                <div class="relative skill-dropdown-container">
                  <button
                    @click="toggleSkillDropdown"
                    type="button"
                    class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent flex justify-between items-center hover:bg-gray-700 transition-colors"
                  >
                    <span v-if="selectedSkillIds.length === 0" class="text-gray-400">Select skills...</span>
                    <span v-else class="text-white">{{ selectedSkillIds.length }} skill(s) selected</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Dropdown Menu -->
                  <div
                    v-if="showSkillDropdown"
                    class="absolute z-10 mt-1 w-full max-h-60 overflow-y-auto bg-gray-800 border border-gray-700 rounded-lg shadow-lg"
                  >
                    <div
                      v-for="skill in skills"
                      :key="skill.id"
                      class="flex items-center px-4 py-2 border-b border-gray-700 last:border-b-0 hover:bg-gray-700/50"
                    >
                      <input
                        type="checkbox"
                        :id="`skill-${skill.id}`"
                        :value="skill.id"
                        v-model="selectedSkillIds"
                        class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-700 bg-gray-800 rounded"
                      />
                      <Label :for="`skill-${skill.id}`" class="ml-2 text-sm text-gray-300 cursor-pointer flex-1">
                        {{ skill.name }}
                      </Label>
                    </div>
                  </div>
                </div>

                <!-- Selected Skills Display -->
                <div v-if="selectedSkillIds.length > 0" class="mt-2 flex flex-wrap gap-2">
                  <div
                    v-for="skillId in selectedSkillIds"
                    :key="skillId"
                    class="inline-flex items-center bg-cyan-900/50 text-cyan-200 text-xs px-2 py-1 rounded-lg"
                  >
                    {{ getSkillName(skillId) }}
                    <button
                      @click="removeSkill(skillId)"
                      type="button"
                      class="ml-1 text-cyan-300 hover:text-white hover:bg-cyan-900/30 rounded px-1 transition-colors"
                    >
                      &times;
                    </button>
                  </div>
                </div>
                <div v-if="selectedSkillIds.length === 0" class="mt-2 text-xs text-red-400">
                  Please select at least one skill
                </div>
              </div>

              <!-- Assessment Duration -->
              <div>
                <Label for="assessmentDuration" class="text-gray-300">Assessment Duration (minutes)</Label>
                <div class="flex items-center">
                  <Input
                    type="number"
                    id="assessmentDuration"
                    v-model="assessmentDuration"
                    min="5"
                    max="180"
                    class="w-32"
                    required
                  />
                </div>
                <div class="mt-1 text-xs text-gray-400">
                  Set the time limit for completing this assessment
                </div>
              </div>

              <!-- Question Selection Mode -->
              <div>
                <Label class="text-gray-300">Question Selection Mode</Label>
                <div class="flex space-x-4">
                  <div class="flex items-center">
                    <input
                      type="radio"
                      id="dynamicMode"
                      name="questionSelectionMode"
                      value="dynamic"
                      v-model="questionSelectionMode"
                      class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-700 bg-gray-800"
                    />
                    <Label for="dynamicMode" class="ml-2 text-sm text-gray-300">
                      Dynamic
                      <span class="block text-xs text-gray-400">Questions randomly selected for each session</span>
                    </Label>
                  </div>
                  <div class="flex items-center">
                    <input
                      type="radio"
                      id="fixedMode"
                      name="questionSelectionMode"
                      value="fixed"
                      v-model="questionSelectionMode"
                      class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-700 bg-gray-800"
                    />
                    <Label for="fixedMode" class="ml-2 text-sm text-gray-300">
                      Fixed
                      <span class="block text-xs text-gray-400">Same questions for all sessions</span>
                    </Label>
                  </div>
                </div>
                <div class="mt-2 text-xs text-indigo-300 bg-indigo-900/20 p-2 rounded border border-indigo-800/30">
                  <p><strong>Note:</strong></p>
                  <ul class="list-disc list-inside mt-1 space-y-1">
                    <li><strong>Dynamic mode:</strong> Questions are randomly selected from the skill's question pool for each session.</li>
                    <li><strong>Fixed mode:</strong> After creating the assessment, you'll need to go to "Add Fixed Questions" to select specific questions that will be used for all sessions.</li>
                  </ul>
                </div>
              </div>

              <!-- Loading indicator -->
              <div v-if="isLoading" class="flex justify-center items-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-cyan-500"></div>
                <span class="ml-3 text-gray-300">Creating assessment...</span>
              </div>

              <!-- Error message -->
              <Alert v-if="errorMessage" variant="error">
                <AlertDescription>{{ errorMessage }}</AlertDescription>
              </Alert>

              <!-- Success message -->
              <Alert v-if="successMessage" variant="success">
                <AlertDescription>{{ successMessage }}</AlertDescription>
              </Alert>

              <!-- Assessment details after creation -->
              <div v-if="createdAssessmentDetails" class="mt-4 bg-gray-800/70 border border-gray-700 rounded-lg p-4">
                <h3 class="text-cyan-400 font-medium mb-2">Assessment Details:</h3>
                <div class="space-y-2 text-sm text-gray-300">
                  <p><span class="text-gray-400">Assessment Name:</span> {{ createdAssessmentDetails.assessment_base_name }}</p>
                  <p><span class="text-gray-400">Assessment Description:</span> {{ createdAssessmentDetails.assessment_description || assessmentDescription }}</p>
                  <p><span class="text-gray-400">Assessment ID:</span> {{ createdAssessmentDetails.assessment_id }}</p>
                  <p><span class="text-gray-400">Duration:</span> {{ createdAssessmentDetails.duration || assessmentDuration }} minutes</p>
                  <p><span class="text-gray-400">Question Selection Mode:</span>
                    <span class="capitalize">{{ createdAssessmentDetails.question_selection_mode }}</span>
                    <span v-if="createdAssessmentDetails.question_selection_mode === 'fixed'" class="ml-2 text-xs text-indigo-300">
                      (Go to "Add Fixed Questions" to assign specific questions)
                    </span>
                  </p>
                  <div v-if="createdAssessmentDetails.skill_ids && createdAssessmentDetails.skill_ids.length > 0">
                    <p class="text-gray-400 mb-1">Skills:</p>
                    <div class="flex flex-wrap gap-2 mb-2">
                      <span
                        v-for="skillId in createdAssessmentDetails.skill_ids"
                        :key="skillId"
                        class="inline-block bg-cyan-900/30 text-cyan-200 text-xs px-2 py-1 rounded-lg"
                      >
                        {{ getSkillName(skillId) }}
                      </span>
                    </div>
                  </div>
                  <p v-if="createdAssessmentDetails.total_questions_available">
                    <span class="text-gray-400">Available Questions:</span> {{ createdAssessmentDetails.total_questions_available }}
                  </p>
                  <div class="mt-4">
                    <p class="text-cyan-400 font-medium">Next Steps:</p>
                    <p class="text-gray-300" v-if="questionSelectionMode === 'fixed'">
                      Add fixed questions to this assessment.
                    </p>
                    <p class="text-gray-300" v-else>
                      Use the "Generate Sessions" menu to create session codes for this assessment.
                    </p>
                    <div class="mt-2">
                      <!-- For Fixed Mode: Show only Add Fixed Questions button -->
                      <template v-if="questionSelectionMode === 'fixed'">
                        <Button
                          @click="navigateTo('/add-fixed-assessment-questions')"
                          variant="assessmentGenerate"
                          size="skillButton"
                        >
                          Add Fixed Questions
                        </Button>
                      </template>
                      <!-- For Dynamic Mode: Show Generate Sessions button -->
                      <template v-else>
                        <Button
                          @click="navigateTo('/generate-sessions')"
                          variant="generalAction"
                          size="skillButton"
                        >
                          Go to Generate Sessions
                        </Button>
                      </template>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Submit button -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  variant="assessmentGenerate"
                  size="skillButton"
                  :disabled="isLoading"
                >
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create Assessment
                  </span>
                </Button>
              </div>
            </form>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

const router = useRouter();
const navigateTo = (path) => {
  router.push(path);
};

// Form data
const assessmentName = ref('');
const assessmentDescription = ref('');
const selectedSkillIds = ref([]); // Changed to array for multiple selection
const questionSelectionMode = ref('dynamic'); // Default to dynamic mode
const assessmentDuration = ref(30); // Default duration in minutes
const skills = ref([]);
const isLoading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');
const createdAssessmentDetails = ref(null);
const showSkillDropdown = ref(false); // For dropdown toggle

// Helper functions for skill management
const getSkillName = (skillId) => {
  const skill = skills.value.find(s => s.id === skillId);
  return skill ? skill.name : `Skill ${skillId}`;
};

const removeSkill = (skillId) => {
  selectedSkillIds.value = selectedSkillIds.value.filter(id => id !== skillId);
};

const toggleSkillDropdown = () => {
  showSkillDropdown.value = !showSkillDropdown.value;
};

// Close dropdown when clicking outside
const closeDropdownOnOutsideClick = (event) => {
  if (showSkillDropdown.value && !event.target.closest('.skill-dropdown-container')) {
    showSkillDropdown.value = false;
  }
};

// Fetch skills from API
const fetchSkills = async () => {
  try {
    isLoading.value = true;
    const response = await axios.get('/api/admin/skills');
    skills.value = response.data;
  } catch (error) {
    errorMessage.value = `Failed to fetch skills: ${error.message}`;
    console.error('Error fetching skills:', error);
  } finally {
    isLoading.value = false;
  }
};

// Create assessment via API
const createAssessment = async () => {
  // Enhanced validation
  if (!assessmentName.value || !assessmentDescription.value || selectedSkillIds.value.length === 0) {
    errorMessage.value = 'Please fill in all required fields and select at least one skill';
    return;
  }

  // Additional validation
  if (assessmentName.value.trim().length < 3) {
    errorMessage.value = 'Assessment name must be at least 3 characters long';
    return;
  }

  if (assessmentDescription.value.trim().length < 10) {
    errorMessage.value = 'Assessment description must be at least 10 characters long';
    return;
  }

  if (selectedSkillIds.value.length > 10) {
    errorMessage.value = 'Please select no more than 10 skills for an assessment';
    return;
  }

  isLoading.value = true;
  errorMessage.value = '';
  successMessage.value = '';
  createdAssessmentDetails.value = null;

  try {
    // Get the primary skill for the topic (using the first selected skill)
    const primarySkillId = selectedSkillIds.value[0];
    const primarySkill = skills.value.find(skill => skill.id === primarySkillId);

    if (!primarySkill) {
      throw new Error('Selected skill not found');
    }

    // Get current username (in a real app, this would come from auth)
    const username = localStorage.getItem('username') || 'admin_user';

    // Call the API to create the quiz/assessment
    const response = await axios.post('/api/admin/quiz', {
      quiz_name: assessmentName.value,
      topic: assessmentDescription.value, // Use the new description field instead of skill description
      user_id: username,
      skill_ids: selectedSkillIds.value.map(id => parseInt(id)), // Convert all selected skill IDs to integers
      question_selection_mode: questionSelectionMode.value,
      duration: parseInt(assessmentDuration.value), // Add duration in minutes
      create_single_assessment: true // Create only one assessment instead of mock and final
    });

    // Extract data from response
    const responseData = response.data;

    // Store the response details for display
    createdAssessmentDetails.value = responseData;

    successMessage.value = `Successfully created "${assessmentName.value}" assessment!`;

    // Reset form after success
    assessmentName.value = '';
    selectedSkillIds.value = [];
    assessmentDuration.value = 30; // Reset to default duration

  } catch (error) {
    console.error('Error creating assessment:', error);

    // Handle different types of errors with specific messages
    if (error.response) {
      // Server responded with an error status
      const status = error.response.status;
      const detail = error.response.data?.detail || error.response.data?.message || 'Unknown server error';

      switch (status) {
        case 400:
          errorMessage.value = `Invalid request: ${detail}`;
          break;
        case 409:
          errorMessage.value = `Assessment already exists: ${detail}`;
          break;
        case 500:
          errorMessage.value = `Server error: ${detail}`;
          break;
        default:
          errorMessage.value = `Error (${status}): ${detail}`;
      }
    } else if (error.request) {
      // Network error - no response received
      errorMessage.value = 'Network error: Unable to connect to the server. Please check your internet connection and try again.';
    } else {
      // Client-side error or validation error
      errorMessage.value = error.message || 'An unexpected error occurred while creating the assessment';
    }

    // Clear any success message on error
    successMessage.value = '';
    createdAssessmentDetails.value = null;
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchSkills();

  // Add event listener for closing dropdown when clicking outside
  document.addEventListener('click', closeDropdownOnOutsideClick);
});

// Clean up event listener when component is unmounted
onUnmounted(() => {
  document.removeEventListener('click', closeDropdownOnOutsideClick);
});
</script>

<style scoped>
/* No need for animation styles as they're now in HerbitBackground component */
</style>
