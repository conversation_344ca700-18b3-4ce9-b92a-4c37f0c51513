
# Database Entity-Relationship Diagram

This document provides a visual representation of the database schema for the Herbit system.

## Core Entities and Relationships

```mermaid
erDiagram
    skills {
        int id PK
        text name UK
        text description
        timestamp created_at
        timestamp updated_at
    }

    assessments {
        int id PK
        text name
        text description
        boolean is_final
        int duration_minutes
        int total_questions
        jsonb composition
        int passing_score
        timestamp created_at
        timestamp updated_at
    }

    assessment_skills {
        int assessment_id PK,FK
        int skill_id PK,FK
    }

    questions {
        int que_id PK
        text topic
        text level
        text question
        jsonb options
        text answer
        text[] topics
        int skill_id FK
        timestamp time
    }

    final_questions {
        int final_que_id PK
        int que_id FK
        text topic
        text level
        text question
        jsonb options
        text answer
    }

    users {
        int id PK
        text external_id UK
        text email
        text display_name
        timestamp created_at
        timestamp updated_at
    }

    sessions {
        int id PK
        char(6) code UK
        int user_id FK
        int assessment_id FK
        text status
        int score
        timestamp started_at
        timestamp completed_at
        timestamp created_at
        timestamp updated_at
    }

    user_answers {
        int id PK
        int session_id FK
        int question_id FK
        text user_answer
        boolean is_correct
        int score
        int time_taken
        timestamp created_at
    }

    user_assessment {
        int id PK
        varchar(255) user_id
        text topic
        varchar(50) level
        varchar(50) quiz_type
        int que_id FK
        text question
        jsonb options
        text user_answer
        text correct_answer
        text result
        int score
        timestamp time
    }

    quiz_creation_logs {
        int id PK
        varchar(255) user_id
        varchar(255) assessment_name
        varchar(255) assessment_description
        int total_questions
        timestamp time
        int assessment_id FK
    }

    skills ||--o{ questions : "has"
    skills ||--o{ assessment_skills : "included_in"
    assessments ||--o{ assessment_skills : "includes"
    assessments ||--o{ sessions : "has"
    assessments ||--o{ quiz_creation_logs : "logged_in"
    questions ||--o{ final_questions : "selected_for"
    questions ||--o{ user_answers : "answered"
    questions ||--o{ user_assessment : "answered_legacy"
    users ||--o{ sessions : "takes"
    sessions ||--o{ user_answers : "contains"
```

## Schema Evolution Notes

1. The system has undergone migration from a legacy structure (using `quiz_codes` and `user_assessment` tables) to a more normalized structure with proper relationships.

2. The `quiz_codes` table has been dropped (migration 022), and its foreign key constraint from the `questions` table has been removed (migration 021).

3. The `user_assessment` table is being phased out in favor of the `sessions` and `user_answers` tables, which provide better normalization and relationship modeling.

4. The `assessment_skills` table was initially created in migration 011 and then recreated in migration 024, suggesting a schema refactoring.

## Constraints and Validations

- Questions have a level constraint to ensure values are only 'easy', 'intermediate', or 'advanced'
- Skills have a description length constraint (minimum 20 characters)
- Sessions have a status constraint to ensure values are only 'pending', 'in_progress', 'completed', or 'expired'
- User assessment results are constrained to 'Correct', 'Incorrect', or 'Timeout'
- Assessment composition must sum to the total number of questions (enforced by a trigger)

## Indexes

The following indexes have been created to improve query performance:

- `idx_sessions_code` on `sessions(code)`
- `idx_questions_skill_id` on `questions(skill_id)`
- `idx_questions_level` on `questions(level)`
- `idx_user_answers_session_id` on `user_answers(session_id)`
- `idx_user_answers_question_id` on `user_answers(question_id)`
- `idx_sessions_user_id` on `sessions(user_id)`
- `idx_sessions_assessment_id` on `sessions(assessment_id)`
- `idx_assessment_skills_skill_id` on `assessment_skills(skill_id)`
