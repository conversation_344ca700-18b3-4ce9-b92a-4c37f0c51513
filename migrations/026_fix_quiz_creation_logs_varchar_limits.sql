-- Fix VARCHAR(255) constraints in quiz_creation_logs table
-- This migration addresses the StringDataRightTruncation error when skill descriptions exceed 255 characters

-- Change VARCHAR(255) columns to TEXT to allow longer content
ALTER TABLE quiz_creation_logs 
ALTER COLUMN user_id TYPE TEXT,
ALTER COLUMN assessment_name TYPE TEXT,
ALTER COLUMN assessment_description TYPE TEXT;

-- Add a comment to document the change
COMMENT ON TABLE quiz_creation_logs IS 'Quiz creation logs with TEXT columns to support longer descriptions';
COMMENT ON COLUMN quiz_creation_logs.assessment_description IS 'Assessment description - changed from VARCHAR(255) to TEXT to support longer skill descriptions';
